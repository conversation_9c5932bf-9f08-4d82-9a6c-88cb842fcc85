# 团子 (<PERSON><PERSON><PERSON>) 设计系统文档

## 概述

团子应用的设计系统基于三大核心支柱：**可信 (Trustworthy)**、**美观 (Beautiful)** 与 **活力 (Vibrant)**。本文档详细介绍了完整的设计语言、组件库和使用指南。

## 🎨 设计原则

### 1. 可信 (Trustworthy)
- **专业感**: 使用深色调和专业的配色方案建立信任感
- **一致性**: 统一的设计语言确保用户体验的可预测性
- **清晰度**: 明确的视觉层次和信息架构

### 2. 美观 (Beautiful)
- **现代感**: 采用现代化的设计趋势和视觉效果
- **优雅**: 通过柔和的色彩和优雅的设计提升美感
- **呼吸感**: 合理的间距和留白营造舒适的视觉体验

### 3. 活力 (Vibrant)
- **动态性**: 运用鲜艳的色彩和动态效果增加活力
- **互动性**: 丰富的微交互和动画反馈
- **趣味性**: 通过图标、表情符号等元素增加趣味

## 🌈 色彩系统

### 主色调
```typescript
primary: '#6366F1'        // 现代感的靛蓝色，体现可信与活力
primaryLight: '#818CF8'   // 柔和的浅色变体
primaryDark: '#4F46E5'    // 深色变体，增强对比度
primarySoft: '#E0E7FF'    // 极浅的背景色
```

### 辅助色
```typescript
secondary: '#EC4899'      // 活力粉色，增加趣味性
tertiary: '#F59E0B'       // 温暖橙色，营造友好氛围
```

### 活力色彩
```typescript
vibrant: {
  coral: '#FF6B6B',       // 珊瑚橙 - 用于重要提示
  mint: '#4ECDC4',        // 薄荷绿 - 用于成功状态
  lemon: '#FFE66D',       // 柠檬黄 - 用于警告和高亮
  lavender: '#A8E6CF',    // 薰衣草绿 - 用于次要信息
  peach: '#FFB3BA',       // 桃色 - 用于温和提示
  sky: '#87CEEB',         // 天空蓝 - 用于信息展示
}
```

### 状态色
```typescript
success: '#10B981'        // 成功状态
warning: '#F59E0B'        // 警告状态
error: '#EF4444'          // 错误状态
info: '#3B82F6'           // 信息状态
```

### 中性色
```typescript
// 更温暖的灰色调
gray50: '#FAFAFA'
gray100: '#F5F5F5'
gray200: '#EEEEEE'
// ... 更多层级
```

## 📝 字体系统

### 字体族
```typescript
fontFamily: {
  primary: 'System',      // 系统默认字体，确保兼容性
  heading: 'System',      // 标题字体
  mono: 'Courier',        // 等宽字体
}
```

### 字体大小
```typescript
fontSize: {
  xs: 10,
  sm: 12,
  base: 14,
  lg: 16,
  xl: 18,
  '2xl': 20,
  '3xl': 24,
  '4xl': 28,
  '5xl': 32,
  '6xl': 36,
  '7xl': 42,
  '8xl': 48,
}
```

### 字体权重
```typescript
fontWeight: {
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
}
```

## 📏 间距系统

```typescript
spacing: {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
  '6xl': 64,
  '7xl': 80,
  '8xl': 96,
}
```

## 🔲 圆角系统

```typescript
borderRadius: {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
}
```

## 🌟 阴影系统

### 标准阴影
```typescript
shadows: {
  sm: { shadowOpacity: 0.06, shadowRadius: 3, elevation: 2 },
  md: { shadowOpacity: 0.08, shadowRadius: 6, elevation: 4 },
  lg: { shadowOpacity: 0.12, shadowRadius: 12, elevation: 8 },
  xl: { shadowOpacity: 0.15, shadowRadius: 20, elevation: 12 },
}
```

### 特殊阴影
```typescript
glow: {
  shadowColor: '#6366F1',
  shadowOpacity: 0.3,
  shadowRadius: 8,
}
```

## 🎬 动画系统

```typescript
animations: {
  duration: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
}
```

## 🧩 核心组件

### Button 组件

#### 变体 (Variants)
- `primary`: 主要操作按钮
- `secondary`: 次要操作按钮
- `tertiary`: 第三级操作按钮
- `outline`: 轮廓按钮
- `ghost`: 幽灵按钮
- `danger`: 危险操作按钮
- `success`: 成功操作按钮
- `warning`: 警告操作按钮
- `vibrant`: 活力按钮

#### 尺寸 (Sizes)
- `small`: 小尺寸 (36px 高度)
- `medium`: 中等尺寸 (44px 高度)
- `large`: 大尺寸 (52px 高度)
- `xl`: 超大尺寸 (60px 高度)

#### 使用示例
```tsx
import { Button } from '../components';

// 基础用法
<Button 
  title="确认" 
  onPress={handleConfirm}
  variant="primary"
  size="medium"
/>

// 带图标的按钮
<Button 
  title="创建房间" 
  onPress={handleCreate}
  variant="vibrant"
  icon={<CreateIcon />}
  iconPosition="left"
/>

// 全宽度按钮
<Button 
  title="开始游戏" 
  onPress={handleStart}
  variant="primary"
  size="large"
  fullWidth
  shadow
/>
```

### Card 组件

#### 变体 (Variants)
- `default`: 默认卡片
- `elevated`: 提升卡片（更强阴影）
- `outlined`: 轮廓卡片
- `filled`: 填充卡片
- `vibrant`: 活力卡片

#### 尺寸 (Sizes)
- `small`: 小间距
- `medium`: 中等间距
- `large`: 大间距

#### 使用示例
```tsx
import { Card } from '../components';

// 基础卡片
<Card variant="elevated">
  <Text>卡片内容</Text>
</Card>

// 可点击卡片
<Card 
  variant="vibrant"
  onPress={handlePress}
  glowEffect
>
  <Text>点击我</Text>
</Card>

// 自定义样式卡片
<Card 
  variant="outlined"
  style={{ borderLeftWidth: 4, borderLeftColor: '#6366F1' }}
>
  <Text>自定义边框</Text>
</Card>
```

### Badge 组件

#### 变体 (Variants)
- `success`: 成功徽章
- `warning`: 警告徽章
- `error`: 错误徽章
- `info`: 信息徽章
- `neutral`: 中性徽章
- `primary`: 主要徽章
- `secondary`: 次要徽章
- `vibrant`: 活力徽章

#### 使用示例
```tsx
import { Badge } from '../components';

// 基础徽章
<Badge text="Pro" variant="primary" />

// 带图标的徽章
<Badge 
  text="在线" 
  variant="success" 
  icon={<OnlineIcon />}
  size="small"
/>

// 轮廓徽章
<Badge 
  text="新功能" 
  variant="vibrant" 
  outlined
  rounded
/>
```

## 📱 页面设计模式

### 主页设计 (HomeScreen)
- **欢迎区域**: 个性化问候和用户状态
- **快速操作**: 加入房间的便捷入口
- **功能导航**: 卡片式功能导航，支持权限控制
- **状态展示**: 订阅状态的清晰展示

### 游戏房间设计 (RoomScreen)
- **信息面板**: 实时游戏状态和计时器
- **主要内容**: 游戏画布和聊天区域
- **交互反馈**: 丰富的动画和状态反馈

### 订阅页面设计 (SubscriptionScreen)
- **信任建立**: 专业的计划对比展示
- **价值突出**: 清晰的功能差异化
- **行动引导**: 明确的升级路径

## 🎯 微交互设计

### 按钮交互
- **按下效果**: 0.95倍缩放 + 阴影变化
- **悬停状态**: 颜色加深 + 阴影增强
- **加载状态**: 旋转动画 + 文字变化

### 卡片交互
- **点击反馈**: 0.98倍缩放
- **悬停效果**: 阴影提升
- **状态变化**: 平滑的颜色过渡

### 消息动画
- **新消息**: 滑入动画 + 透明度变化
- **正确答案**: 庆祝动画 + 特殊样式
- **输入状态**: 边框颜色变化

## 🔧 开发指南

### 主题使用
```tsx
import { theme } from '../styles/theme';

// 使用颜色
backgroundColor: theme.colors.primary

// 使用间距
padding: theme.spacing.lg

// 使用字体
fontSize: theme.typography.fontSize.xl
fontWeight: theme.typography.fontWeight.bold

// 使用阴影
...theme.shadows.md
```

### 组件导入
```tsx
// 统一导入
import { Button, Card, Badge, Typography, Screen } from '../components';

// 主题导入
import { theme } from '../styles/theme';
```

### 样式组合
```tsx
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.xl,
    ...theme.shadows.md,
  },
});
```

## 📋 最佳实践

### 1. 颜色使用
- 优先使用主题色彩系统
- 避免硬编码颜色值
- 注意颜色对比度和可访问性

### 2. 间距规范
- 使用统一的间距系统
- 保持视觉层次的一致性
- 适当的留白提升可读性

### 3. 动画原则
- 动画时长适中（150-350ms）
- 使用原生驱动提升性能
- 避免过度动画影响用户体验

### 4. 组件复用
- 优先使用现有组件
- 通过props实现变体
- 保持组件的单一职责

## 🚀 未来规划

### 短期目标
- [ ] 完善组件库文档
- [ ] 添加更多微交互
- [ ] 优化动画性能

### 长期目标
- [ ] 支持深色模式
- [ ] 国际化支持
- [ ] 无障碍功能增强

---

*本文档将随着设计系统的演进持续更新。如有疑问或建议，请联系设计团队。*
