# 团子组件使用示例

本文档提供了团子应用中各个UI组件的详细使用示例和最佳实践。

## 🔘 Button 组件示例

### 基础按钮
```tsx
import { Button } from '../components';

// 主要操作按钮
<Button 
  title="创建房间" 
  onPress={handleCreateRoom}
  variant="primary"
  size="medium"
/>

// 次要操作按钮
<Button 
  title="取消" 
  onPress={handleCancel}
  variant="outline"
  size="medium"
/>
```

### 特殊效果按钮
```tsx
// 活力按钮 - 用于重要的游戏操作
<Button 
  title="开始游戏" 
  onPress={handleStartGame}
  variant="vibrant"
  size="large"
  fullWidth
  shadow
  hapticFeedback
/>

// 圆形按钮
<Button 
  title="+" 
  onPress={handleAdd}
  variant="primary"
  size="medium"
  rounded
/>
```

### 带图标的按钮
```tsx
// 左侧图标
<Button 
  title="加入房间" 
  onPress={handleJoinRoom}
  variant="secondary"
  icon={<JoinIcon />}
  iconPosition="left"
/>

// 右侧图标
<Button 
  title="下一步" 
  onPress={handleNext}
  variant="primary"
  icon={<ArrowRightIcon />}
  iconPosition="right"
/>
```

## 🃏 Card 组件示例

### 基础卡片
```tsx
import { Card, Typography } from '../components';

// 简单内容卡片
<Card variant="elevated">
  <Typography variant="h3">标题</Typography>
  <Typography variant="body1" color="textSecondary">
    这是卡片的内容描述
  </Typography>
</Card>
```

### 交互式卡片
```tsx
// 可点击的导航卡片
<Card 
  variant="elevated"
  onPress={() => navigation.navigate('EventDesigner')}
  style={{ borderLeftWidth: 4, borderLeftColor: theme.colors.secondary }}
>
  <View style={styles.cardContent}>
    <Text style={styles.cardIcon}>🎨</Text>
    <View>
      <Text style={styles.cardTitle}>环节设计器</Text>
      <Text style={styles.cardDescription}>设计自定义游戏环节</Text>
    </View>
  </View>
</Card>
```

### 特殊效果卡片
```tsx
// 发光效果卡片 - 用于当前状态
<Card 
  variant="vibrant"
  glowEffect
  style={styles.currentPlanCard}
>
  <Text style={styles.planTitle}>当前计划</Text>
  <Text style={styles.planName}>Pro</Text>
</Card>

// 轮廓卡片 - 用于次要信息
<Card 
  variant="outlined"
  style={styles.infoCard}
>
  <Text>次要信息内容</Text>
</Card>
```

## 🏷️ Badge 组件示例

### 状态徽章
```tsx
import { Badge } from '../components';

// 成功状态
<Badge text="在线" variant="success" size="small" />

// 警告状态
<Badge text="即将到期" variant="warning" size="medium" />

// 错误状态
<Badge text="已断线" variant="error" size="small" />
```

### 订阅等级徽章
```tsx
// 免费用户
<Badge text="Free" variant="neutral" size="medium" />

// Pro用户
<Badge text="Pro" variant="primary" size="medium" />

// Max用户
<Badge text="Max" variant="vibrant" size="medium" />
```

### 特殊样式徽章
```tsx
// 轮廓徽章
<Badge 
  text="新功能" 
  variant="primary" 
  outlined 
  size="small"
/>

// 圆形徽章
<Badge 
  text="5" 
  variant="error" 
  rounded 
  size="xs"
/>

// 带图标的徽章
<Badge 
  text="当前计划" 
  variant="success" 
  icon={<CheckIcon />}
  size="medium"
/>
```

## 📄 Typography 组件示例

```tsx
import { Typography } from '../components';

// 页面标题
<Typography variant="h1" color="textPrimary">
  欢迎使用团子
</Typography>

// 章节标题
<Typography variant="h2" color="textPrimary">
  功能介绍
</Typography>

// 正文内容
<Typography variant="body1" color="textSecondary">
  这是正文内容，用于详细描述功能特性。
</Typography>

// 小字说明
<Typography variant="caption" color="textTertiary">
  * 此功能需要Pro订阅
</Typography>
```

## 📱 Screen 组件示例

```tsx
import { Screen } from '../components';

// 基础页面布局
<Screen>
  <Text style={styles.title}>页面标题</Text>
  <Text style={styles.content}>页面内容</Text>
</Screen>

// 自定义背景色的页面
<Screen 
  backgroundColor={theme.colors.primarySoft}
  padding={theme.spacing.xl}
>
  <Text>自定义页面内容</Text>
</Screen>

// 不可滚动的页面
<Screen scrollable={false}>
  <View style={styles.fixedLayout}>
    <Text>固定布局内容</Text>
  </View>
</Screen>
```

## 🎮 游戏组件示例

### 聊天消息组件
```tsx
// 自己的消息
<View style={styles.myMessageBubble}>
  <Text style={styles.messageSender}>我</Text>
  <Text style={styles.messageText}>这是我发送的消息</Text>
</View>

// 他人的消息
<View style={styles.theirMessageBubble}>
  <Text style={styles.messageSender}>玩家A</Text>
  <Text style={styles.messageText}>这是其他玩家的消息</Text>
</View>

// 正确答案消息（带特殊效果）
<View style={[styles.messageBubble, styles.correctGuessBubble]}>
  <Text style={styles.messageSender}>玩家B</Text>
  <Text style={styles.messageText}>苹果</Text>
  <Text style={styles.correctIcon}>🎉</Text>
</View>
```

### 游戏状态组件
```tsx
// 计时器组件
<Animated.View 
  style={[
    styles.timerContainer,
    { transform: [{ scale: timerPulse }] }
  ]}
>
  <Text style={[styles.timerText, { color: getTimerColor() }]}>
    {timeLeft}s
  </Text>
  <View style={[styles.timerProgress, { backgroundColor: getTimerColor() }]} />
</Animated.View>

// 单词显示组件
<Animated.View 
  style={[
    styles.wordPanel,
    {
      opacity: wordReveal,
      transform: [{ scale: wordReveal }],
    }
  ]}
>
  <Text style={styles.wordText}>苹果</Text>
  <Text style={styles.roleText}>🤔 猜猜这是什么</Text>
</Animated.View>
```

## 🎨 订阅组件示例

### 订阅计划卡片
```tsx
<Card
  variant="elevated"
  style={[
    styles.planCard,
    isCurrentPlan && styles.currentPlanCard
  ]}
  glowEffect={isCurrentPlan}
>
  {/* 计划头部 */}
  <View style={[styles.planHeader, { backgroundColor: plan.color }]}>
    <View style={styles.planHeaderContent}>
      <Text style={styles.planName}>{plan.name}</Text>
      {isCurrentPlan && (
        <Badge 
          text="当前计划" 
          variant="success" 
          size="small"
        />
      )}
    </View>
    <Text style={styles.planPrice}>{plan.priceText}</Text>
  </View>

  {/* 计划内容 */}
  <View style={styles.planContent}>
    {/* 功能列表 */}
    {plan.features.map((feature, index) => (
      <View key={index} style={styles.featureItem}>
        <Text style={styles.featureIcon}>✓</Text>
        <Text style={styles.featureText}>{feature}</Text>
      </View>
    ))}
  </View>
</Card>
```

### 订阅状态卡片
```tsx
<Card 
  variant="elevated" 
  style={[styles.statusCard, { borderTopColor: currentPlan.color }]}
  glowEffect={currentPlan.level !== 'Free'}
>
  <View style={styles.statusHeader}>
    <Text style={styles.statusTitle}>当前订阅计划</Text>
    <Badge 
      text={currentPlan.level}
      variant={getBadgeVariant(currentPlan.level)}
      size="medium"
    />
  </View>
  
  <View style={styles.statusContent}>
    <Text style={[styles.statusLevel, { color: currentPlan.color }]}>
      {currentPlan.name}
    </Text>
    <Text style={styles.statusPrice}>{currentPlan.priceText}</Text>
  </View>
</Card>
```

## 🎯 最佳实践

### 1. 组件组合
```tsx
// 好的做法：组合使用组件
<Card variant="elevated">
  <View style={styles.cardHeader}>
    <Typography variant="h3">标题</Typography>
    <Badge text="新" variant="primary" size="small" />
  </View>
  <Typography variant="body1" color="textSecondary">
    描述内容
  </Typography>
  <Button 
    title="了解更多" 
    variant="outline" 
    size="small"
    onPress={handleLearnMore}
  />
</Card>
```

### 2. 响应式设计
```tsx
// 根据屏幕尺寸调整组件
const isSmallScreen = width < 400;

<Button 
  title="创建房间" 
  variant="primary"
  size={isSmallScreen ? "medium" : "large"}
  fullWidth={isSmallScreen}
/>
```

### 3. 状态管理
```tsx
// 根据状态显示不同的组件变体
<Badge 
  text={subscriptionLevel}
  variant={subscriptionLevel === 'Free' ? 'neutral' : 'primary'}
  size="medium"
/>

<Button 
  title={isLoading ? "加载中..." : "确认"}
  variant="primary"
  loading={isLoading}
  disabled={isLoading}
  onPress={handleConfirm}
/>
```

### 4. 动画集成
```tsx
// 结合动画使用组件
<Animated.View style={{ opacity: fadeAnim }}>
  <Card variant="elevated">
    <Typography variant="h3">动画卡片</Typography>
  </Card>
</Animated.View>
```

## 🔧 自定义样式

### 扩展组件样式
```tsx
// 使用 StyleSheet.flatten 合并样式
<Card 
  style={StyleSheet.flatten([
    styles.baseCard,
    { borderLeftColor: theme.colors.primary }
  ])}
>
  <Text>自定义样式卡片</Text>
</Card>
```

### 主题色彩应用
```tsx
const styles = StyleSheet.create({
  customButton: {
    backgroundColor: theme.colors.vibrant.coral,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
    ...theme.shadows.md,
  },
  customText: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.white,
  },
});
```

---

*这些示例展示了如何有效使用团子应用的组件库。更多详细信息请参考设计系统文档。*
